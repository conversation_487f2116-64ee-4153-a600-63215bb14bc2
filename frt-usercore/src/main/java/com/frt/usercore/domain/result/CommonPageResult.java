/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.frt.usercore.domain.result;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonPageResult<T> implements Serializable {
    private static final long serialVersionUID = 6745582089930558874L;
    /**
     * 总数
     */
    private Integer total;
    /**
     * 列表数据
     */
    private List<T> list;

    public static <T> CommonPageResult<T> success(Integer total, List<T> list) {
        return new CommonPageResult<>(total, list);
    }

    public static <T> CommonPageResult<T> emptyModel() {
        return new CommonPageResult<>(0, new ArrayList<>());
    }
}

