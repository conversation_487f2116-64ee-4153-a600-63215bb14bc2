package com.frt.usercore.service.impl;

import com.frt.usercore.domain.param.common.CommonAddressCodeListQueryParam;
import com.frt.usercore.domain.param.common.CommonUnityCategoryListQueryParam;
import com.frt.usercore.domain.result.common.CommonAddressCodeListQueryResult;
import com.frt.usercore.domain.result.common.CommonUnityCategoryListQueryResult;
import com.frt.usercore.service.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CommonServiceImpl implements CommonService {

    // todo xujw

    @Override
    public CommonAddressCodeListQueryResult queryAddressCodeList(CommonAddressCodeListQueryParam param) {

        CommonAddressCodeListQueryResult result = new CommonAddressCodeListQueryResult();
        result.setCityCode("ok");
        return result;
    }

    @Override
    public CommonUnityCategoryListQueryResult queryUnityCategoryList(CommonUnityCategoryListQueryParam param) {
        return null;
    }
}
