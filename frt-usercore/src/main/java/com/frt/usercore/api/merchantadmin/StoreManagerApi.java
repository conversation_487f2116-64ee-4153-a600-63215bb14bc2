package com.frt.usercore.api.merchantadmin;

import com.frt.usercore.domain.param.storemanager.StoreInfoAddParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoQueryParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoUpdateParam;
import com.frt.usercore.domain.param.storemanager.StoreListQueryParam;
import com.frt.usercore.domain.result.CommonPageResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoAddResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoQueryResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoUpdateResult;
import com.frt.usercore.domain.result.storemanager.StoreListQueryResult;
import com.frt.usercore.service.StoreManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/store")
public class StoreManagerApi {

    @Autowired
    private StoreManagerService storeManagerService;

    /**
     * 查询门店列表
     * @param param
     * @return
     */
    @PostMapping("/query/list")
    public CommonPageResult<StoreListQueryResult> queryStoreList(@RequestBody StoreListQueryParam param) {
        return storeManagerService.queryStoreList(param);
    }

    /**
     * 新增门店
     *
     * @param param
     * @return
     */
    @PostMapping("/add/info")
    public StoreInfoAddResult addStoreInfo(@RequestBody StoreInfoAddParam param) {
        return storeManagerService.addStoreInfo(param);
    }
    /**
     * 修改门店
     *
     * @param param
     * @return
     */
    @PostMapping("/update/info")
    public StoreInfoUpdateResult updateStoreInfo(@RequestBody StoreInfoUpdateParam param) {
        return storeManagerService.updateStoreInfo(param);
    }
    /**
     * 门店详情
     *
     * @param param
     * @return
     */
    @PostMapping("/query/info")
    public StoreInfoQueryResult queryStoreInfo(@RequestBody StoreInfoQueryParam param) {
        return storeManagerService.queryStoreInfo(param);
    }

}