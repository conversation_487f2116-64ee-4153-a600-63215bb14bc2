package com.frt.usercore.api.merchantadmin;

import com.frt.usercore.domain.param.common.CommonAddressCodeListQueryParam;
import com.frt.usercore.domain.param.common.CommonUnityCategoryListQueryParam;
import com.frt.usercore.domain.result.common.CommonAddressCodeListQueryResult;
import com.frt.usercore.domain.result.common.CommonUnityCategoryListQueryResult;
import com.frt.usercore.service.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/common")
public class CommonApi {

    @Autowired
    private CommonService commonService;

    /**
     * 查询地址列表
     * @param param
     * @return
     */
    @PostMapping("/query/address-code-list")
    public CommonAddressCodeListQueryResult queryAddressCodeList(@RequestBody CommonAddressCodeListQueryParam param) {
        return commonService.queryAddressCodeList(param);
    }

    /**
     * 查询分类列表
     * @param param
     * @return
     */
    @PostMapping("/query/unity-category-list")
    public CommonUnityCategoryListQueryResult queryUnityCategoryList(@RequestBody CommonUnityCategoryListQueryParam param) {
        return commonService.queryUnityCategoryList(param);
    }

}