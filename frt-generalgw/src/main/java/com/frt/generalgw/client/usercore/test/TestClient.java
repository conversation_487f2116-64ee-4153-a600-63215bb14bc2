/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.test;

import com.frt.generalgw.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @version TestClient.java, v 0.1 2025-08-26 20:07 wangyi
 */
@FeignClient(value = "frt-usercore-dev", configuration = {FeignConfig.class}) // nacos 服务 id
public interface TestClient {

    /**
     * 测试请求
     *
     * @return
     */
    @GetMapping("/api/test/hello")
    String hello();
}