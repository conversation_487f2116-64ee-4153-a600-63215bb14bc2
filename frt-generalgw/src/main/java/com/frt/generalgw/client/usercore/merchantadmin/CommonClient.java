/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.merchantadmin;

import com.frt.generalgw.config.FeignConfig;
import com.frt.generalgw.domain.param.merchantadmin.common.CommonAddressCodeListQueryParam;
import com.frt.generalgw.domain.param.merchantadmin.common.CommonUnityCategoryListQueryParam;
import com.frt.generalgw.domain.result.merchantadmin.common.CommonAddressCodeListQueryResult;
import com.frt.generalgw.domain.result.merchantadmin.common.CommonUnityCategoryListQueryResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(value = "frt-usercore-dev", configuration = {FeignConfig.class}) // nacos 服务 id
public interface CommonClient {

    /**
     * 查询地址列表
     *
     * @return
     */
    @PostMapping("/api/common/query/address-code-list")
    CommonAddressCodeListQueryResult queryAddressCodeList(CommonAddressCodeListQueryParam param);

    /**
     * 查询类目列表
     * @param param
     * @return
     */
    @PostMapping("/api/common/query/unity-category-list")
    CommonUnityCategoryListQueryResult queryUnityCategoryList(CommonUnityCategoryListQueryParam param);
}